// NOVEMBER DISCOUNT DISPLAY

console.log('🏷️ 🎯 Pricing and slashes script loaded');

// Debounce timeout variable
let handleResultsTimeout;
const discountedProductParents = new Set(); // Store parent elements with discounted prices

// All trips £150 off (exceptions - Machu P £100 Off, Olympus £100 Off, Training weekends £100 Off, Winter Skills £50 off)

// Discount data
const productDiscounts = [
 {"url": "/holidays/annapurna-circuit-trek", "discount": 150.0},
 {"url": "/holidays/everest-base-camp-trek", "discount": 150.0},
 {"url": "/holidays/everest-base-camp-via-gokyo-valley", "discount": 150.0},
 {"url": "/holidays/everest-three-passes-trek", "discount": 150.0},
 {"url": "/holidays/island-peak-expedition", "discount": 150.0},
 {"url": "/holidays/k2-base-camp-trek", "discount": 150.0},
 {"url": "/holidays/kilimanjaro-the-long-way", "discount": 150.0},
 {"url": "/holidays/langtang-valley-trek", "discount": 150.0},
 {"url": "/holidays/machu-picchu-via-inca-trail", "discount": 100.0},
 {"url": "/holidays/machu-picchu-via-tomacaya-route-the-hidden-valley", "discount": 100.0},
 {"url": "/holidays/mt-olympus-myths-and-legends-weekender", "discount": 100.0},
 {"url": "/holidays/mt-toubkal-roof-of-the-north-weekender", "discount": 150.0},
 {"url": "/holidays/north-wales-training-weekend", "discount": 100.0},
 {"url": "/holidays/scotland-winter-skills-weekend", "discount": 50.0},
 {"url": "/holidays/tour-du-mont-blanc-trek", "discount": 150.0},
 {"url": "/holidays/uk-training-weekend", "discount": 100.0},
 {"url": "/holidays/ultimate-island-peak-and-everest-base-camp-expedition", "discount": 150.0},
 {"url": "/holidays/ultimate-mera-peak-expedition", "discount": 150.0},
 {"url": "/holidays/annapurna-base-camp-trek", "discount": 150.0},
 {"url": "/holidays/mt-toubkal-roof-of-the-north-8-day-trek", "discount": 150.0}
];

// List of CSS selectors
/* const cssSelectors = [
  '.sppb-addon-holiday .zen-card__body .zen-flex-end .row div > .zen-pill:nth-of-type(1) span',
  '.sppb-addon-holiday .zen-card__body .zen-flex-end .row div > .zen-pill:nth-of-type(3)',
  '.zen-holiday__info-bar .col:nth-of-type(4) .zen-text',
  '[id *="price-year-item-"] .col:nth-of-type(2) .zen-accordion__content-cell:nth-of-type(1) .zen-pill',
  '[id *="price-year-item-"] .col:nth-of-type(2) .zen-accordion__content-cell:nth-of-type(3) .zen-pill',
  '.search-item .col-md-7 .zen-flex-end .zen-pill:nth-of-type(1) .ng-binding',
  '.search-item .col-md-7 .zen-flex-end .zen-pill:nth-of-type(3)',
]; */

// List of CSS selectors
const cssSelectors = [
'div.holiday-grid-container .zen-price > div > div > span:nth-child(1) > span',
'div.holiday-grid-container .zen-price > div > div > span:nth-child(3) > span',
'#content > div.zen-holiday > div.zen-hero > div.trip-details-bar .price-value .price-full',
'#content > div.zen-holiday > div.zen-hero > div.trip-details-bar .price-value .price-monthly',
'[id*="price-month-item"] .zen-text--price',
'[id*="price-month-item"] .zen-text--price-instalment-value',
'[id*="price-year-item"] .zen-text--price',
'[id*="price-year-item"] .zen-text--price-instalment-value',
'#results .zen-card .zen-price span:nth-of-type(1) > .ng-binding',
'#results .zen-card .zen-price span:nth-of-type(3) > .ng-binding'
];

// Helper functions
function normalizePrice(priceStr, productUrl) {
  console.log('🏷️ 💰 Normalizing price:', priceStr, 'for URL:', productUrl);
  const match = priceStr.match(/£\s*([\d,]+(?:\.\d+)?)(?:\s*\/\s*(\d+))?/);
  if (match) {
    const price = parseFloat(match[1].replace(/,/g, ''));
    const months = match[2] ? parseInt(match[2], 10) : null;
    const result = { price: isNaN(price) ? 0 : price, months };
    console.log('🏷️ 💰 Normalized result:', result);
    return result;
  }
  const fallbackResult = { price: parseFloat(priceStr.replace(/[^0-9.-]+/g, '')), months: null };
  console.log('🏷️ 💰 Fallback normalized result:', fallbackResult);
  return fallbackResult;
}

function formatPriceWithOriginal(priceStr, newPrice) {
  const prefix = '£';
  const suffix = priceStr.match(/pp/i) ? 'pp' : '';
  const formatted = `${prefix}${Math.ceil(newPrice)}${suffix}`;
  console.log('🏷️ 💷 Formatted price:', formatted, 'from:', newPrice);
  return formatted;
}

function calculatePayMonthly(innerHTML, discount, productUrl) {
  console.log('🏷️ 📅 Calculating monthly payment for:', innerHTML, 'discount:', discount);
  const { price: finalPrice, months } = normalizePrice(innerHTML, productUrl);
  const deposit = 200; // Keep for potential future use
  const wasPrice = (finalPrice * (months || 1) + discount) / (months || 1);
  const result = `<span style="text-decoration: line-through; opacity: .5">${formatPriceWithOriginal(innerHTML, wasPrice)}</span> ${formatPriceWithOriginal(innerHTML, finalPrice)}${months ? ` / ${months} Months` : ''}`;
  console.log('🏷️ 📅 Monthly payment result:', result);
  return result;
}

function calculatePayInFull(innerHTML, discount, productUrl) {
  console.log('🏷️ 💳 Calculating full payment for:', innerHTML, 'discount:', discount);
  const { price: finalPrice } = normalizePrice(innerHTML, productUrl);
  const wasPrice = finalPrice + discount;
  const result = `<span style="text-decoration: line-through; opacity: .5">${formatPriceWithOriginal(innerHTML, wasPrice)}</span> ${formatPriceWithOriginal(innerHTML, finalPrice)}`;
  console.log('🏷️ 💳 Full payment result:', result);
  return result;
}

// Process .zen-holiday__content-box only if URL matches productDiscounts
const processZenHolidayContentBox = () => {
// return; // TEMP DISABLE
  console.log('🏷️ 🏠 Processing zen holiday content box...');
  const currentUrl = window.location.pathname;
  console.log('🏷️ 🏠 Current URL:', currentUrl);
  const matchingProduct = productDiscounts.find(item => item.url === currentUrl);

  if (matchingProduct) {
    console.log(`🏷️ ✅ URL matches productDiscounts: ${currentUrl}`, matchingProduct);

    const appPanels = document.querySelectorAll('.zen-holiday__content-box .zen-holiday__content-item:first-child .col:first-child .zen-media');
    console.log("🏷️ 🔍 Found appPanels elements:", appPanels.length, appPanels);

    if (appPanels.length > 0) {
      appPanels.forEach((appPanel, index) => {
        console.log(`🏷️ 🔧 Processing appPanel [${index}]:`, appPanel);

        appPanel.style.padding = 0;

        // Log existing children
        console.log("🏷️ 👶 Existing children of appPanel:", appPanel.children.length, Array.from(appPanel.children));

        // Remove all child elements safely
        let removedCount = 0;
        while (appPanel.firstChild) {
          appPanel.removeChild(appPanel.firstChild);
          removedCount++;
        }

        console.log(`🏷️ 🗑️ Removed ${removedCount} children from appPanel`);

        // Add the SVG
        const svgImg = document.createElement('img');
        svgImg.src = '/images/2024/11/08/nov_panel.svg'; // Replace with your SVG URL
        svgImg.alt = 'Icon';
        svgImg.classList.add('app-flash');

        appPanel.appendChild(svgImg);
        console.log("🏷️ 🖼️ SVG added to appPanel:", svgImg.src);
      });
    } else {
      console.warn("🏷️ ⚠️ No matching elements found for '.zen-holiday__content-box .zen-holiday__content-item:first-child .col:first-child .zen-media'.");
    }
  } else {
    console.log(`🏷️ ❌ Current URL does not match productDiscounts: ${currentUrl}`);
    console.log('🏷️ 📋 Available product URLs:', productDiscounts.map(p => p.url));
  }
};

// Main function to handle results with debounce
const handleResults = function(eventOrMutations) {
  console.log('🏷️ 🚀 handleResults called with:', eventOrMutations);
  clearTimeout(handleResultsTimeout);

  const debounceDelay = eventOrMutations === "XHR Response Trigger" ? 50 : 0;
  console.log('🏷️ ⏱️ Debounce delay:', debounceDelay + 'ms');

  handleResultsTimeout = setTimeout(() => {
    console.log("🏷️ 🔄 Processing discount prices and corner flashes...");

    // Apply pricing updates
    cssSelectors.forEach((selector, selectorIndex) => {
      console.log(`�️ �🎯 Processing selector [${selectorIndex}]:`, selector);
      const elements = document.querySelectorAll(selector);
      console.log(`�️ �🎯 Found ${elements.length} elements for selector:`, selector);

      elements.forEach((element, elementIndex) => {
        const innerHTML = element.innerHTML.trim();
        console.log(`🏷️ 💡 Processing element [${elementIndex}] innerHTML:`, innerHTML);
        let fullUrl = window.location.pathname;
        console.log('�️ �🌐 Current URL:', fullUrl);

        let product = productDiscounts.find(item => item.url === fullUrl);
        if (!product) {
          console.log('🏷️ 🔍 No direct URL match, checking closest link...');
          const closestLink = element.closest('a[href]');
          if (closestLink) {
            const fallbackUrl = new URL(closestLink.getAttribute('href'), window.location.origin).pathname;
            console.log('🏷️ 🔗 Fallback URL from link:', fallbackUrl);
            product = productDiscounts.find(item => item.url === fallbackUrl);
          }
        }

        if (product) {
          console.log('🏷️ ✅ Product match found:', product);
          const zenCard = element.closest('.zen-card');
          console.log('🏷️ 🎴 Looking for .zen-card parent:', zenCard);

          if (zenCard) {
            console.log('🏷️ ✅ Adding .zen-card to discounted set with discount:', product.discount);
            // Store both the zen-card and its discount value
            discountedProductParents.add({zenCard: zenCard, discount: product.discount});
          } else {
            console.log('🏷️ ⚠️ No .zen-card parent found for this element');
            console.log('🏷️ 🔍 Element ancestors:', element.parentElement, element.parentElement?.parentElement);
          }

          if (innerHTML.includes('/')) {
            console.log('🏷️ 📅 Processing as monthly payment');
            element.innerHTML = calculatePayMonthly(innerHTML, product.discount, product.url);
          } else {
            console.log('🏷️ 💳 Processing as full payment');
            element.innerHTML = calculatePayInFull(innerHTML, product.discount, product.url);
          }
        } else {
          console.log('🏷️ ❌ No product match found for element');
        }
      });
    });

    // Add corner flashes to .zen-card__image elements inside .zen-card parents
    console.log('🏷️ 🌟 Adding corner flashes to', discountedProductParents.size, '.zen-card parents');
    discountedProductParents.forEach((parentData, parentIndex) => {
      const zenCard = parentData.zenCard;
      const discount = parentData.discount;
      console.log(`🏷️ 🌟 Processing .zen-card [${parentIndex}] for corner flash with discount:`, discount, zenCard);

      if (!zenCard) {
        console.log('🏷️ ⚠️ Skipping null .zen-card');
        return;
      }

      // Only look for .zen-card__image elements inside this .zen-card
      const zenCardImages = zenCard.querySelectorAll('.zen-card__image');
      console.log(`🏷️ 🖼️ Found ${zenCardImages.length} .zen-card__image elements in .zen-card`);

      if (zenCardImages.length === 0) {
        console.log('🏷️ 🔍 No .zen-card__image found, checking .zen-card structure:', zenCard.innerHTML.substring(0, 200));
      }

      zenCardImages.forEach((zenCardImage, imageIndex) => {
        console.log(`🏷️ 🖼️ Processing .zen-card__image [${imageIndex}]:`, zenCardImage);
        zenCardImage.style.position = 'relative';

        // Check if corner flash already exists
        if (zenCardImage.querySelector('.campaign-corner-flash')) {
          console.log('🏷️ ⚠️ Corner flash already exists, skipping');
          return;
        }

        // Create dynamic corner flash path based on discount amount
        const cornerFlashPath = `/images/2025/06/30/corner-${discount}.svg`;
        console.log('🏷️ 🎯 Using corner flash path:', cornerFlashPath);

        const svgImg = document.createElement('img');
        svgImg.src = cornerFlashPath;
        svgImg.alt = `${discount} Discount`;
        svgImg.classList.add('campaign-corner-flash');
        svgImg.style.position = 'absolute';
        svgImg.style.top = '0';
        svgImg.style.right = '0';
        svgImg.style.zIndex = '10';

        zenCardImage.appendChild(svgImg);
        console.log(`🏷️ ✨ Corner flash added to .zen-card__image with ${discount} discount`);
      });
    });

    processZenHolidayContentBox(); // Process .zen-holiday__content-box last
  }, debounceDelay);
};

// Function to monitor HTTP calls, focusing on search and holiday tab endpoints
function monitorHttpCalls() {
  console.log('🏷️ 🔍 Setting up HTTP monitoring...');
  const searchUrl = "https://evertrek.co.uk/evertrek/holiday/_search";
  console.log('🏷️ 🎯 Monitoring search URL:', searchUrl);
  console.log('🏷️ 🎯 Monitoring holiday tab AJAX requests (com_sppagebuilder + holidaytabs)');

  const originalOpen = XMLHttpRequest.prototype.open;
  const originalSend = XMLHttpRequest.prototype.send;

  XMLHttpRequest.prototype.open = function (method, url, ...rest) {
    this._url = url;
    this._method = method;
    console.log('🏷️ 📡 XHR opened:', method, url);
    return originalOpen.apply(this, [method, url, ...rest]);
  };

  XMLHttpRequest.prototype.send = function (...args) {
    // Existing search functionality - keep exactly as original
    if (this._method === "POST" && this._url === searchUrl) {
      console.log('🏷️ 🎯 Monitoring search POST request to:', this._url);
      this.addEventListener("load", () => {
        console.log('🏷️ 📨 Search XHR response received, status:', this.status);
        if (this.status >= 200 && this.status < 300) {
          console.log('🏷️ ✅ Successful search response, triggering handleResults');
          handleResults("XHR Response Trigger");
        } else {
          console.log('🏷️ ❌ Failed search response, not triggering handleResults');
        }
      });
    }

    // Check if this is a holiday tabs AJAX request by examining the request data
    let isHolidayTabRequest = false;
    if (args.length > 0 && args[0]) {
      const requestData = args[0];
      // Check if it's a FormData or string containing holidaytabs parameters
      if (requestData instanceof FormData) {
        isHolidayTabRequest = requestData.get('addon') === 'holidaytabs' && requestData.get('option') === 'com_sppagebuilder';
      } else if (typeof requestData === 'string') {
        isHolidayTabRequest = requestData.includes('addon=holidaytabs') && requestData.includes('option=com_sppagebuilder');
      }
    }

    if (isHolidayTabRequest) {
      console.log('🏷️ 🎯 Monitoring holiday tab AJAX request to:', this._url);
      this.addEventListener("load", () => {
        console.log('🏷️ 📨 Holiday tab AJAX response received, status:', this.status);
        if (this.status >= 200 && this.status < 300) {
          console.log('🏷️ ✅ Successful holiday tab response, triggering handleResults');
          // Add a small delay to ensure DOM is updated before processing
          setTimeout(() => {
            handleResults("Holiday Tab AJAX Response Trigger");
          }, 100);
        } else {
          console.log('🏷️ ❌ Failed holiday tab response, not triggering handleResults');
        }
      });
    }

    return originalSend.apply(this, args);
  };
}

// Initialize logging script on DOMContentLoaded
document.addEventListener("DOMContentLoaded", () => {
  console.log('🏷️ 🚀 DOM Content Loaded - initializing script');

  const searchResults = document.querySelector("#search-results");
  console.log('🏷️ 🔍 Search results element:', searchResults);

  if (!searchResults) {
    console.log('🏷️ 📄 No search results found, running initial load');
    handleResults("Initial Load");
  } else {
    console.log('🏷️ 🔍 Search results found, setting up HTTP monitoring');
    monitorHttpCalls(); // Start monitoring HTTP calls
  }

  // Use MutationObserver to dynamically detect `.zen-holiday__content-box`
  console.log('🏷️ 👀 Setting up MutationObserver for zen-holiday__content-box');
  const observer = new MutationObserver((mutations) => {
    console.log('🏷️ 🔄 DOM mutations detected:', mutations.length);
    const appPanels = document.querySelectorAll('.zen-holiday__content-box .zen-holiday__content-item:first-child .col:first-child .zen-media');
    if (appPanels.length > 0) {
      console.log("🏷️ ✅ Detected appPanels dynamically:", appPanels.length, appPanels);
      observer.disconnect(); // Stop observing after finding elements
      console.log('🏷️ 🛑 MutationObserver disconnected');
      processZenHolidayContentBox(); // Process the detected elements
    }
  });

  observer.observe(document.body, { childList: true, subtree: true });
  console.log('🏷️ 👀 MutationObserver started, watching document.body');
});
// NOVEMBER DISCOUNT DISPLAY

// Debounce timeout variable
let handleResultsTimeout;
const discountedProductParents = new Set(); // Store parent elements with discounted prices

// Discount data
const productDiscounts = [
  {"url": "/holidays/kilimanjaro-the-long-way", "discount": 400.0},
  {"url": "/holidays/machu-picchu-via-inca-trail", "discount": 100.0},
  {"url": "/holidays/machu-picchu-via-tomacaya-route-the-hidden-valley", "discount": 100.0},
  {"url": "/holidays/everest-base-camp-trek", "discount": 400.0},
  {"url": "/destinations/asia/nepal/everest-base-camp-expedition", "discount": 400.0},
  {"url": "/holidays/mt-toubkal-roof-of-the-north-weekender", "discount": 50.0},
  {"url": "/holidays/mt-toubkal-roof-of-the-north-8-day-trek", "discount": 50.0},
  {"url": "/holidays/langtang-valley-trek", "discount": 400.0},
  {"url": "/holidays/island-peak-expedition", "discount": 400.0},
  {"url": "/holidays/annapurna-circuit-trek", "discount": 400.0},
  {"url": "/holidays/ultimate-mera-peak-expedition", "discount": 400.0},
  {"url": "/holidays/ultimate-island-peak-and-everest-base-camp-expedition", "discount": 400.0},
  {"url": "/holidays/everest-three-passes-trek", "discount": 400.0},
  {"url": "/holidays/everest-base-camp-via-gokyo-valley", "discount": 400.0},
  {"url": "/holidays/tour-du-mont-blanc-trek", "discount": 400.0},
  {"url": "/holidays/k2-base-camp-trek", "discount": 400.0},
  {"url": "/holidays/gran-paradiso-hut-to-hut-summit-trek", "discount": 400.0},
  {"url": "/holidays/annapurna-base-camp-trek", "discount": 400.0},
  {"url": "/holidays/ultimate-aconcagua", "discount": 400.0}
];

// List of CSS selectors
const cssSelectors = [
  '.sppb-addon-holiday .zen-card__body .zen-flex-end .row div > .zen-pill:nth-of-type(1) span',
  '.sppb-addon-holiday .zen-card__body .zen-flex-end .row div > .zen-pill:nth-of-type(3)',
  '.zen-holiday__info-bar .col:nth-of-type(4) .zen-text',
  '[id *="price-year-item-"] .col:nth-of-type(2) .zen-accordion__content-cell:nth-of-type(1) .zen-pill',
  '[id *="price-year-item-"] .col:nth-of-type(2) .zen-accordion__content-cell:nth-of-type(3) .zen-pill',
  '.search-item .col-md-7 .zen-flex-end .zen-pill:nth-of-type(1) .ng-binding',
  '.search-item .col-md-7 .zen-flex-end .zen-pill:nth-of-type(3)',
];

// Helper functions
function normalizePrice(priceStr, productUrl) {
  const match = priceStr.match(/£\s*([\d,]+(?:\.\d+)?)(?:\s*\/\s*(\d+))?/);
  if (match) {
    const price = parseFloat(match[1].replace(/,/g, ''));
    const months = match[2] ? parseInt(match[2], 10) : null;
    return { price: isNaN(price) ? 0 : price, months };
  }
  return { price: parseFloat(priceStr.replace(/[^0-9.-]+/g, '')), months: null };
}

function formatPriceWithOriginal(priceStr, newPrice) {
  const prefix = '£';
  const suffix = priceStr.match(/pp/i) ? 'pp' : '';
  return `${prefix}${Math.ceil(newPrice)}${suffix}`;
}

function calculatePayMonthly(innerHTML, discount, productUrl) {
  const { price: finalPrice, months } = normalizePrice(innerHTML, productUrl);
  const deposit = 200;
  const wasPrice = (finalPrice * (months || 1) + discount) / (months || 1);
  return `<span style="text-decoration: line-through; opacity: .5">${formatPriceWithOriginal(innerHTML, wasPrice)}</span> ${formatPriceWithOriginal(innerHTML, finalPrice)}${months ? ` / ${months} Months` : ''}`;
}

function calculatePayInFull(innerHTML, discount, productUrl) {
  const { price: finalPrice } = normalizePrice(innerHTML, productUrl);
  const wasPrice = finalPrice + discount;
  return `<span style="text-decoration: line-through; opacity: .5">${formatPriceWithOriginal(innerHTML, wasPrice)}</span> ${formatPriceWithOriginal(innerHTML, finalPrice)}`;
}

// Process .zen-holiday__content-box only if URL matches productDiscounts
const processZenHolidayContentBox = () => {
// return; // TEMP DISABLE
  const currentUrl = window.location.pathname;
  const matchingProduct = productDiscounts.find(item => item.url === currentUrl);

  if (matchingProduct) {
    console.log(`URL matches productDiscounts: ${currentUrl}`, matchingProduct);

    const appPanels = document.querySelectorAll('.zen-holiday__content-box .zen-holiday__content-item:first-child .col:first-child .zen-media');
    console.log("Found appPanels elements:", appPanels);

    if (appPanels.length > 0) {
      appPanels.forEach((appPanel, index) => {
        console.log(`Processing appPanel [${index}]:`, appPanel);

        appPanel.style.padding = 0;

        // Log existing children
        console.log("Existing children of appPanel:", appPanel.children);

        // Remove all child elements safely
        while (appPanel.firstChild) {
          appPanel.removeChild(appPanel.firstChild);
        }

        console.log("All children removed from appPanel");

        // Add the SVG
        const svgImg = document.createElement('img');
        svgImg.src = '/images/2024/11/08/nov_panel.svg'; // Replace with your SVG URL
        svgImg.alt = 'Icon';
        svgImg.classList.add('app-flash');

        appPanel.appendChild(svgImg);
        console.log("SVG added to appPanel:", appPanel);
      });
    } else {
      console.warn("No matching elements found for '.zen-holiday__content-box .zen-holiday__content-item:first-child .col:first-child .zen-media'.");
    }
  } else {
    console.log(`Current URL does not match productDiscounts: ${currentUrl}`);
  }
};

// Main function to handle results with debounce
const handleResults = function(eventOrMutations) {
  clearTimeout(handleResultsTimeout);

  const debounceDelay = eventOrMutations === "XHR Response Trigger" ? 50 : 0;

  handleResultsTimeout = setTimeout(() => {
    console.log("Processing discount prices and corner flashes...");

    // Apply pricing updates
    cssSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);

      elements.forEach(element => {
        const innerHTML = element.innerHTML.trim();
        let fullUrl = window.location.pathname;

        let product = productDiscounts.find(item => item.url === fullUrl);
        if (!product) {
          const closestLink = element.closest('a[href]');
          if (closestLink) {
            const fallbackUrl = new URL(closestLink.getAttribute('href'), window.location.origin).pathname;
            product = productDiscounts.find(item => item.url === fallbackUrl);
          }
        }

        if (product) {
          discountedProductParents.add(element.closest('.search-item, .sppb-addon-holiday')); // Add parent to set

          if (innerHTML.includes('/')) {
            element.innerHTML = calculatePayMonthly(innerHTML, product.discount, product.url);
          } else {
            element.innerHTML = calculatePayInFull(innerHTML, product.discount, product.url);
          }
        }
      });
    });

    // Add corner flashes
    discountedProductParents.forEach(parent => {
      const imageSelectors = parent.querySelectorAll('.card-image, .zen-card__image');
      imageSelectors.forEach((imageSelector) => {
        imageSelector.style.position = 'relative';

        const svgImg = document.createElement('img');
        svgImg.src = '/images/2024/11/08/nov_corner_flash.svg'; // Replace with your SVG URL
        svgImg.alt = 'Icon';
        svgImg.classList.add('campaign-corner-flash');
        svgImg.style.position = 'absolute';
        svgImg.style.top = '0';
        svgImg.style.right = '0';

        imageSelector.appendChild(svgImg);
      });
    });

    processZenHolidayContentBox(); // Process .zen-holiday__content-box last
  }, debounceDelay);
};

// Function to monitor HTTP calls, focusing on the specific POST to _search
function monitorHttpCalls() {
  const targetUrl = "https://evertrek.co.uk/evertrek/holiday/_search";

  const originalOpen = XMLHttpRequest.prototype.open;
  const originalSend = XMLHttpRequest.prototype.send;

  XMLHttpRequest.prototype.open = function (method, url, ...rest) {
    this._url = url;
    this._method = method;
    return originalOpen.apply(this, [method, url, ...rest]);
  };

  XMLHttpRequest.prototype.send = function (...args) {
    if (this._method === "POST" && this._url === targetUrl) {
      this.addEventListener("load", () => {
        if (this.status >= 200 && this.status < 300) {
          handleResults("XHR Response Trigger");
        }
      });
    }
    return originalSend.apply(this, args);
  };
}

// Initialize logging script on DOMContentLoaded
document.addEventListener("DOMContentLoaded", () => {
  if (!document.querySelector("#search-results")) {
    handleResults("Initial Load");
  } else {
    monitorHttpCalls(); // Start monitoring HTTP calls
  }

  // Use MutationObserver to dynamically detect `.zen-holiday__content-box`
  const observer = new MutationObserver(() => {
    const appPanels = document.querySelectorAll('.zen-holiday__content-box .zen-holiday__content-item:first-child .col:first-child .zen-media');
    if (appPanels.length > 0) {
      console.log("Detected appPanels dynamically:", appPanels);
      observer.disconnect(); // Stop observing after finding elements
      processZenHolidayContentBox(); // Process the detected elements
    }
  });

  observer.observe(document.body, { childList: true, subtree: true });
});
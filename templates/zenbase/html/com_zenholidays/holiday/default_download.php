<div class="zen-holiday__download">
  <div class="container">
	  <div class="row">
	   	<div class="col-12 d-flex flex-wrap text-center justify-content-center align-items-center"">
		   <?php foreach ($holGuide as $guide): ?>
					<?php foreach ($guide->items as $item): ?>
						<img 
							src="https://i.assetzen.net/i/<?php echo $item->images[0]->file_name; ?>/w:150/h:188/q:70.png"
							class="text-center d-none" 
						/>      
						<?php $downloadTitle = ($item->keywords) ? str_replace($item->keywords, '<b>'. $item->keywords .'</b>', $item->title) : $item->title; ?>
						<?php $item->content = JHtml::_('content.prepare', $item->content);?>
							<div class="text-center mt-3 mt-md-0">
								<h3 class="zen-title zen-title--light zen-title--font-light zen-no-transform">
									<?php echo $downloadTitle; ?>
								</h3>
								<div>
									<?php echo $item->content; ?>
								</div>
								<div>
									<?php
									// Check if content contains a valid URL, otherwise use default
									$customUrl = trim(strip_tags($item->content));
									$isValidUrl = !empty($customUrl) && (
										strpos($customUrl, 'http://') === 0 ||
										strpos($customUrl, 'https://') === 0 ||
										strpos($customUrl, '/') === 0
									);
									$fullUrl = $isValidUrl ? $customUrl : '/brochure-download?automation=' . $item->xreference . '&subject=' . urlencode($name);
									?>
										<a
											href="<?= $fullUrl; ?>"
											class="zen-btn zen-btn--full-size text-center">
											<?php echo JText::_('ZEN_HOLIDAY_GUIDE_TEXT'); ?>
										</a>
								</div>
							</div>
					<?php endforeach;?>
				<?php endforeach;?>
			</div>
		</div>
	</div>
</div>
